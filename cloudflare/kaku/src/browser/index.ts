import { CDP } from './simple-cdp';
import { CaptchaBoundingBox } from '../agent/types/extract-result';

import { scriptManifest } from './manifest';

/**
 * Frame information tracked by the CDP Frame Manager
 */
interface FrameInfo {
  id: string;
  parentId?: string;
  session: CDP;
  sessionId?: string;
  url?: string;
  isMainFrame: boolean;
  scriptsInjected: Set<string>;
}

/**
 * Script information for injection
 */
interface ScriptInfo {
  identifier: string;
  source: string;
  worldName: string;
}

/**
 * CDP Frame Manager - Mimics Puppeteer's FrameManager behavior
 * Handles frame discovery, script injection, and lifecycle management
 */
export class CDPFrameManager {
  private rootSession: CDP;
  private targetSessionId?: string;
  private frames = new Map<string, FrameInfo>(); // frameId -> frame info
  private sessions = new Map<string, CDP>(); // sessionId -> session
  private scriptsToEvaluate = new Map<string, ScriptInfo>(); // scriptId -> script info

  constructor(rootSession: CDP, targetSessionId?: string) {
    this.rootSession = rootSession;
    this.targetSessionId = targetSessionId;
    this.setupEventListeners(rootSession);
  }

  private setupEventListeners(session: CDP): void {
    // Listen for frame events
    session.Page.addEventListener('frameAttached', async (event: any) => {
      this.onFrameAttached(event.params.frameId, event.params.parentFrameId);
    });

    session.Page.addEventListener('frameNavigated', async (event: any) => {
      this.onFrameNavigated(event.params.frame);
    });

    session.Page.addEventListener('frameDetached', async (event: any) => {
      this.onFrameDetached(event.params.frameId, event.params.reason);
    });

    // Listen for target events (OOP iframes)
    session.Target.addEventListener('attachedToTarget', async (event: any) => {
      /**
       * Event {
          params: {
            sessionId: '6B3FD8A6D805A4928045767836E57030',
            targetInfo: {
              targetId: '757EB050A1FEF49D1FE14A81ECD591E7',
              type: 'iframe',
              title: '',
              url: '',
              attached: true,
              canAccessOpener: false,
              browserContextId: '0FEBBB139C74660D53E0BADA5DF45F71'
            },
            waitingForDebugger: false
          },
          sessionId: '4BC6821121B85E8CFB2EA4D2927BEE83',
          type: 'Target.attachedToTarget',
          eventPhase: 2,
          composed: false,
          bubbles: false,
          cancelable: false,
          defaultPrevented: false,
          returnValue: true,
          currentTarget: Connection {},
          target: Connection {},
          srcElement: Connection {},
          timeStamp: 0,
          isTrusted: false,
          cancelBubble: false
        }
       */
      await this.onTargetAttached(event);
    });
  }

  async initialize(): Promise<void> {
    console.log('🔧 Initializing CDP Frame Manager...');
    // Setup auto-attach for OOP iframes
    await this.rootSession.Target.setAutoAttach(
      {
        autoAttach: true,
        waitForDebuggerOnStart: false,
        flatten: true,
      },
      this.targetSessionId,
    );

    console.log('✓ CDP Frame Manager initialized');
  }

  private onFrameAttached(
    frameId: string,
    parentFrameId: string,
    session = this.rootSession,
    sessionId?: string,
  ): void {
    if (this.frames.has(frameId)) {
      console.log(`📎 Frame ${frameId} already exists, updating session`);
      const frame = this.frames.get(frameId)!;
      frame.session = session;
      frame.sessionId = sessionId || this.targetSessionId;
      return;
    }

    const frame: FrameInfo = {
      id: frameId,
      parentId: parentFrameId,
      session: session,
      sessionId: sessionId || this.targetSessionId, // Store sessionId for CDP calls
      url: undefined,
      isMainFrame: !parentFrameId,
      scriptsInjected: new Set(),
    };

    this.frames.set(frameId, frame);
    console.log(`📎 Frame attached: ${frameId} (parent: ${parentFrameId || 'none'})`);

    // Apply existing scripts to this frame
    this.applyScriptsToFrame(frame);
  }

  private onFrameNavigated(
    framePayload: any,
    session = this.rootSession,
    sessionId?: string,
  ): void {
    const frameId = framePayload.id;
    const isMainFrame = !framePayload.parentId;
    console.log(`🧭 Frame navigated: ${frameId} -> ${framePayload.url}`);

    let frame = this.frames.get(frameId);
    if (!frame) {
      frame = {
        id: frameId,
        parentId: framePayload.parentId,
        session: session,
        sessionId: sessionId || this.targetSessionId, // Store sessionId for CDP calls
        url: framePayload.url,
        isMainFrame: isMainFrame,
        scriptsInjected: new Set(),
      };
      this.frames.set(frameId, frame);
    } else {
      frame.url = framePayload.url;
      frame.session = session;
      frame.sessionId = sessionId || this.targetSessionId; // Update sessionId
      // Clear injected scripts on navigation
      frame.scriptsInjected.clear();
    }

    console.log(`🧭 Frame navigated: ${frameId} -> ${framePayload.url}`);

    // Apply existing scripts to this frame after navigation
    this.applyScriptsToFrame(frame);
  }

  private onFrameDetached(frameId: string, reason: string): void {
    const frame = this.frames.get(frameId);
    if (!frame) {
      return;
    }

    switch (reason) {
      case 'remove':
        // Only remove the frame if the reason for the detached event is
        // an actual removal of the frame.
        console.log(`📎 Frame detached (removed): ${frameId}`);
        this.frames.delete(frameId);
        break;
      case 'swap':
        // For frames that become OOP iframes, the reason would be 'swap'.
        // Don't remove the frame, just emit a swapped event.
        console.log(`🔄 Frame swapped: ${frameId}`);
        this.onFrameSwapped(frame);
        break;
      default:
        // Fallback for unknown reasons - treat as removal
        console.log(`📎 Frame detached (${reason || 'unknown'}): ${frameId}`);
        this.frames.delete(frameId);
        break;
    }
  }

  private onFrameSwapped(frame: FrameInfo): void {
    console.log(`🔄 Frame swapped: ${frame.id} - maintaining frame identity`);
    // Frame is swapped but not removed - this typically happens when
    // a frame becomes an OOP iframe or vice versa.
    // We keep the frame in our collection but it may get a new session later.

    // Clear injected scripts since the frame context is changing
    frame.scriptsInjected.clear();

    // The frame will be updated with a new session when Target.attachedToTarget
    // is fired or when it's reattached with a new session
  }

  private async onTargetAttached(event: any): Promise<void> {
    const { sessionId: iframeSessionId, targetInfo } = event.params;

    console.log('🎯 New target attached:', {
      iframeSessionId,
      type: targetInfo.type,
      url: targetInfo.url,
      targetId: targetInfo.targetId,
    });

    if (targetInfo.type !== 'iframe') {
      return;
    }

    try {
      // For iframe sessions, we use the root session but pass the iframe sessionId to all CDP calls
      this.sessions.set(iframeSessionId, this.rootSession);

      // Get the frame that this target represents
      const frame = this.frames.get(targetInfo.targetId);
      if (frame) {
        frame.session = this.rootSession;
        frame.sessionId = iframeSessionId;
        console.log(
          `🔄 Updated frame ${targetInfo.targetId} with session and sessionId ${iframeSessionId}`,
        );
      }

      // Initialize the session (like void this.initialize(target._session()!, frame))
      await this.initializeSession(this.rootSession, frame, iframeSessionId);
    } catch (error) {
      console.warn(
        `⚠️ Failed to setup iframe session ${iframeSessionId}:`,
        (error as Error).message,
      );
    }
  }

  /**
   * Initialize a session (enable domains and apply scripts)
   * This mirrors the initialize method in Puppeteer's FrameManager
   */
  private async initializeSession(
    session: CDP,
    frame?: FrameInfo,
    explicitSessionId?: string,
  ): Promise<void> {
    console.log('🔧 Initializing session...');

    try {
      const sessionId = explicitSessionId || frame?.sessionId || this.targetSessionId;

      // Enable required domains for the iframe session
      await session.Page.enable(undefined, sessionId);
      await session.Runtime.enable(undefined, sessionId);

      console.log('✓ Session domains enabled');

      // Apply existing scripts to the frame if it exists
      if (frame) {
        await this.applyScriptsToFrame(frame);
        console.log(`✓ Applied scripts to frame ${frame.id}`);
      }
    } catch (error) {
      console.warn('⚠️ Failed to initialize session:', (error as Error).message);
    }
  }

  /**
   * Mimics Puppeteer's evaluateOnNewDocument method
   * This is the key method that implements the FrameManager approach
   */
  async evaluateOnNewDocument(source: string): Promise<{ identifier: string }> {
    console.log('📝 Registering script for evaluation on new document...');

    // Step 1: Register script with main frame (like Puppeteer does)
    const { identifier } = await this.rootSession.Page.addScriptToEvaluateOnNewDocument(
      {
        source,
        worldName: 'kaku-world',
      },
      this.targetSessionId,
    );

    // Step 2: Store script info for future frames
    const scriptInfo: ScriptInfo = {
      identifier,
      source,
      worldName: 'kaku-world',
    };
    this.scriptsToEvaluate.set(identifier, scriptInfo);

    console.log(`✓ Registered script with identifier: ${identifier}`);

    // Step 3: Apply to ALL existing frames (this is the key insight from FrameManager)
    await this.applyScriptToAllFrames(scriptInfo);

    return { identifier };
  }

  /**
   * Apply a script to all currently known frames
   * This mimics the Promise.all(this.frames().map(...)) pattern from FrameManager
   */
  private async applyScriptToAllFrames(scriptInfo: ScriptInfo): Promise<void> {
    const frameArray = Array.from(this.frames.values());
    console.log(`🔄 Applying script to ${frameArray.length} existing frames...`);

    await Promise.all(
      frameArray.map(async (frame) => {
        return await this.applyScriptToFrame(frame, scriptInfo);
      }),
    );

    console.log(`✓ Script applied to all ${frameArray.length} frames`);
  }

  /**
   * Apply scripts to a specific frame
   */
  private async applyScriptsToFrame(frame: FrameInfo): Promise<void> {
    const scripts = Array.from(this.scriptsToEvaluate.values());
    if (scripts.length === 0) return;

    console.log(`📋 Applying ${scripts.length} scripts to frame ${frame.id}...`);

    await Promise.all(
      scripts.map(async (scriptInfo) => {
        return await this.applyScriptToFrame(frame, scriptInfo);
      }),
    );
  }

  /**
   * Apply a single script to a single frame
   */
  private async applyScriptToFrame(frame: FrameInfo, scriptInfo: ScriptInfo): Promise<void> {
    if (frame.scriptsInjected.has(scriptInfo.identifier)) {
      return;
    }

    try {
      // Use the frame's sessionId for CDP calls
      await frame.session.Page.addScriptToEvaluateOnNewDocument(
        {
          source: scriptInfo.source,
          worldName: scriptInfo.worldName,
        },
        frame.sessionId,
      );

      try {
        await frame.session.Runtime.evaluate(
          {
            expression: scriptInfo.source,
            includeCommandLineAPI: false,
            silent: true,
            returnByValue: false,
          },
          frame.sessionId,
        );
        console.log(
          `✓ Injected script ${scriptInfo.identifier} into current document of frame ${frame.id}`,
        );
      } catch (evalError) {
        // This might fail if the frame isn't ready yet, which is okay
        console.log(
          `ℹ️ Could not inject into current document of frame ${frame.id}: ${(evalError as Error).message}`,
        );
      }

      frame.scriptsInjected.add(scriptInfo.identifier);
      console.log(
        `✓ Applied script ${scriptInfo.identifier} to frame ${frame.id} (future + current)`,
      );
    } catch (error) {
      console.warn(`⚠️ Failed to apply script to frame ${frame.id}:`, (error as Error).message);
    }
  }

  /**
   * Get all frames (mimics FrameManager.frames() method)
   */
  getAllFrames(): FrameInfo[] {
    return Array.from(this.frames.values());
  }

  /**
   * Get main frame
   */
  getMainFrame(): FrameInfo | null {
    for (const frame of this.frames.values()) {
      if (frame.isMainFrame) {
        return frame;
      }
    }
    return null;
  }
}

/**
 * Gets the hashed filename from the imported manifest
 * @param baseUrl The base URL for the API endpoint
 * @param filename The original filename
 * @returns The full URL with the hashed filename
 */
export function getHashedScriptUrl(baseUrl: string, filename: string): string {
  // If the filename exists in the manifest, use the hashed version
  if (scriptManifest[filename]) {
    return `${baseUrl}/out/${scriptManifest[filename]}`;
  }

  // Fallback to the original path if the file isn't in the manifest
  return `${baseUrl}/out/${filename}`;
}

export async function injectScript(
  cdpSession: CDP,
  scriptPath: string,
  executionContextId: number,
  sessionId: string,
): Promise<void> {
  const scriptContent = await fetch(scriptPath).then((res) => res.text());
  await cdpSession.Runtime.evaluate(
    {
      expression: scriptContent,
      contextId: executionContextId,
      awaitPromise: true,
    },
    sessionId,
  );
}

export async function injectTensorFlowJS(
  cdpSession: CDP,
  executionContextId: number,
  sessionId: string,
): Promise<void> {
  console.log('→ Fetching TensorFlow.js library');
  const tfUrl = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';
  const response = await fetch(tfUrl);

  if (!response.ok) {
    throw new Error(`Failed to fetch TensorFlow.js: ${response.status} ${response.statusText}`);
  }

  const scriptContent = await response.text();

  console.log('→ Injecting TensorFlow.js library');
  await cdpSession.Runtime.evaluate(
    {
      expression: scriptContent,
      contextId: executionContextId,
      awaitPromise: false,
    },
    sessionId,
  );

  console.log('Fetching TensorFlow.js WASM backend');
  const wasmBackendUrl =
    'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-wasm@4.10.0/dist/tf-backend-wasm.min.js';
  const wasmResponse = await fetch(wasmBackendUrl);

  if (!wasmResponse.ok) {
    throw new Error(
      `Failed to fetch TensorFlow.js WASM backend: ${wasmResponse.status} ${wasmResponse.statusText}`,
    );
  }

  const wasmScriptContent = await wasmResponse.text();

  console.log('Injecting TensorFlow.js WASM backend');
  await cdpSession.Runtime.evaluate(
    {
      expression: wasmScriptContent,
      contextId: executionContextId,
      awaitPromise: false,
    },
    sessionId,
  );

  console.log('Configuring WASM backend');
  const configScript = `
    (async () => {
      try {
        // Set the WASM file path from CDN
        tf.wasm.setWasmPaths('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-wasm@4.10.0/dist/');
        
        // Initialize and set WASM as the backend
        await tf.setBackend('wasm');
        await tf.ready();
        
        console.log('TensorFlow.js WASM backend configured successfully');
        console.log('Current backend:', tf.getBackend());
      } catch (error) {
        console.error('Failed to configure WASM backend:', error);
        // Fallback to default backend if WASM fails
        console.log('Falling back to default backend');
      }
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: configScript,
      contextId: executionContextId,
      awaitPromise: true,
    },
    sessionId,
  );

  console.log('TensorFlow.js library with WASM backend injected');
}

export async function initBrowserController(
  cdpSession: CDP,
  browserWsEndpoint: string,
  executionContextId: number,
  sessionId: string,
  targetId?: string,
): Promise<void> {
  const scriptContent = `
    (async () => {
      await window.browserController.init(
        '${browserWsEndpoint}',
        '${targetId}'
      );
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: scriptContent,
      contextId: executionContextId,
      awaitPromise: true,
    },
    sessionId,
  );
}

export async function initBrowserControllerWithTargetTab(
  cdpSession: CDP,
  browserWsEndpoint: string,
  executionContextId: number,
  sessionId: string,
  targetId: string,
  targetTabWindow: any,
): Promise<void> {
  const scriptContent = `
    (async () => {
      await window.browserController.init(
        '${browserWsEndpoint}',
        '${targetId}',
        ${JSON.stringify(targetTabWindow)}
      );
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: scriptContent,
      contextId: executionContextId,
      awaitPromise: true,
    },
    sessionId,
  );
}

export async function initBrowserControllerProxy(
  cdpSession: CDP,
  browserWsEndpoint: string,
  executionContextId: number,
  sessionId: string,
  targetId: string,
): Promise<void> {
  const scriptContent = `
    (async () => {
      await window.browserController.init(
        '${browserWsEndpoint}',
        '${targetId}'
      );
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: scriptContent,
      contextId: executionContextId,
      awaitPromise: true,
    },
    sessionId,
  );
}

export async function initScreenCropper(
  cdpSession: CDP,
  wsEndpoint: string,
  executionContextId: number,
  viewport: { width: number; height: number },
  sessionId: string,
): Promise<void> {
  const scriptContent = `
    (async () => {
      try {
        console.log('🔧 [initScreenCropper] Starting screen cropper initialization...');
        console.log('🔧 [initScreenCropper] WebSocket endpoint:', '${wsEndpoint}');
        console.log('🔧 [initScreenCropper] Viewport:', { width: ${viewport.width}, height: ${viewport.height} });

        // Check if screen cropper is available
        if (!window.screenCropper) {
          console.error('❌ [initScreenCropper] window.screenCropper is not available');
          throw new Error('Screen cropper not available on window object');
        }

        if (typeof window.screenCropper.init !== 'function') {
          console.error('❌ [initScreenCropper] window.screenCropper.init is not a function');
          console.log('🔍 [initScreenCropper] Available methods:', Object.keys(window.screenCropper));
          throw new Error('Screen cropper init method not available');
        }

        console.log('✅ [initScreenCropper] Screen cropper object and init method found');
        console.log('🚀 [initScreenCropper] Calling init method...');

        await window.screenCropper.init('${wsEndpoint}', { width: ${viewport.width}, height: ${viewport.height} });

        console.log('✅ [initScreenCropper] Screen cropper initialization completed successfully');
        return 'SUCCESS';
      } catch (error) {
        console.error('❌ [initScreenCropper] Error during initialization:', error);
        console.error('❌ [initScreenCropper] Error stack:', error.stack);
        throw error;
      }
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: scriptContent,
      contextId: executionContextId,
      awaitPromise: true,
    },
    sessionId,
  );
}

/**
 * There are different type of captcha that takes up different area due to which the
 *  threshold map needs to be dynamic based on captcha type as well.. (not only viewport).. 
 * We would need a map that is per viewport per captcha type.. but how do we know the captcha type?
 * either we store the captcha type per platform.. or we figure it out from the LLM? Still doubtful.
1. google_captcha_v2
2. google_captcha_v3
3. hCaptcha
4. Cloudflare
5. DataCite
6. Lemin
 */
const VIEWPORT_SCORE_THRESHOLD_MAP: Record<string, number> = {
  // Common mobile viewports
  '375x667': 0.7, // iPhone 6/7/8
  '375x812': 0.6, // iPhone X/11/12 mini
  '414x896': 0.5, // iPhone 11/XR
  '390x844': 0.6, // iPhone 12/13/14
  '428x926': 0.6, // iPhone 12/13/14 Pro Max
  '800x600': 0.5, // Puppeteer default

  // Tablet viewports
  '768x1024': 0.4, // iPad Portrait
  '1024x768': 0.1, // iPad Landscape
  '834x1194': 0.3, // iPad Pro 11" Portrait
  '1194x834': 0.3, // iPad Pro 11" Landscape

  // Desktop viewports
  '1280x720': 0.3, // 720p
  '1366x768': 0.2, // Common laptop
  '1440x900': 0.5, // MacBook Air
  '1536x864': 0.4, // 1.5x scaling
  '1600x900': 0.3, // 16:9 widescreen
  '1680x1050': 0.3, // 16:10 widescreen
  '1920x1080': 0.05, // 1080p
  '2560x1440': 0.02, // 1440p

  // Default fallback
  default: 0.4,
};

function getScoreThresholdForViewport(width: number, height: number): number {
  const viewportKey = `${width}x${height}`;

  if (VIEWPORT_SCORE_THRESHOLD_MAP[viewportKey]) {
    return VIEWPORT_SCORE_THRESHOLD_MAP[viewportKey];
  }

  throw new Error(
    `Viewport ${viewportKey} is not configured in VIEWPORT_SCORE_THRESHOLD_MAP. Please add this viewport to the mapping.`,
  );
}

export async function initTensorFlowDetector(
  cdpSession: CDP,
  executionContextId: number,
  viewport?: { width: number; height: number } | null | undefined,
  sessionId?: string,
): Promise<void> {
  if (!viewport) {
    throw new Error('Viewport is not available');
  }

  const scoreThreshold = getScoreThresholdForViewport(viewport.width, viewport.height);

  const scriptContent = `
    (async () => {
      if (window.tfCaptchaDetector) {
        console.log('Initializing TensorFlow detector with model:');
        await window.tfCaptchaDetector.initialize({
          scoreThreshold: ${scoreThreshold},
          debug: true,
          inputSize: 640
        });
      } else {
        console.error('TensorFlow detector not available');
      }
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: scriptContent,
      contextId: executionContextId,
      awaitPromise: true,
    },
    sessionId,
  );
}

export async function refreshTensorFlowBoundingBox(
  cdpSession: CDP,
  executionContextId: number,
  viewport: { width: number; height: number },
  sessionId: string,
): Promise<void> {
  if (!viewport) {
    console.error('Viewport is not available');
    return;
  }

  const scriptContent = `
    (async () => {
      console.log('Pushing script to refresh bounding box');
      console.log('window.tfCaptchaDetector available:', !!window.tfCaptchaDetector);
      if (window.tfCaptchaDetector) {
        console.log('typeof window.tfCaptchaDetector.refreshBoundingBox:', typeof window.tfCaptchaDetector.refreshBoundingBox);
        await window.tfCaptchaDetector.refreshBoundingBox({
          width: ${viewport.width}, height: ${viewport.height}});
      } else {
        console.error('TensorFlow detector not available');
      }
    })();
  `;

  try {
    await cdpSession.Runtime.evaluate(
      {
        expression: scriptContent,
        contextId: executionContextId,
        awaitPromise: true,
      },
      sessionId,
    );
  } catch (error) {
    console.error('✖ Error executing refreshTensorFlowBoundingBox script:', error);
  }
}

export const updateCropBox = async (
  cdpSession: CDP,
  { x, y, width, height }: CaptchaBoundingBox,
  executionContextId: number,
  sessionId: string,
) => {
  const script = `
  (async () => {
    await window.screenCropper.updateCropBox(
      { x: ${x}, y: ${y}, width: ${width}, height: ${height} }
    );
  })();
`;

  await cdpSession.Runtime.evaluate(
    {
      expression: script,
      awaitPromise: true,
      contextId: executionContextId,
    },
    sessionId,
  );
};

export const initRTCSteaming = async (
  { cdpSession }: { cdpSession: CDP },
  doWebsocketUrl: string,
  executionContextId: number,
  sessionId: string,
) => {
  const viewport = { width: 1024, height: 768 }; // Use fixed viewport dimensions

  const script = `
    (async () => {
      if (window.screenCropper && typeof window.screenCropper.start === 'function') {
        console.log('Starting screen cropper streaming...');
        await window.screenCropper.start({ width: ${viewport.width}, height: ${viewport.height} });
        console.log('Screen cropper streaming started successfully');
      } else {
        console.error('Screen cropper not available for starting streaming');
      }
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: script,
      awaitPromise: true,
      contextId: executionContextId,
    },
    sessionId,
  );

  // await injectMouseTracker(cdpSession, sessionId);
};

const injectMouseTracker = async (cdpSession: CDP, sessionId: string) => {
  await cdpSession.Runtime.evaluate(
    {
      expression: `
    (function () {
      console.log('[KAZEEL] Injecting mouse tracker');
      document.addEventListener('mousemove', (event) => {
        const marker = document.createElement('div');
        Object.assign(marker.style, {
          position: 'absolute',
          left: event.clientX + 'px',
          top: event.clientY + 'px',
          width: '6px',
          height: '6px',
          borderRadius: '50%',
          backgroundColor: 'rgba(255, 0, 0, 0.4)',
          zIndex: '999999',
          pointerEvents: 'none',
          transform: 'translate(-50%, -50%)',
        });
        document.body.appendChild(marker);
        setTimeout(() => marker.remove(), 500);
      });
    })();
  `,
      awaitPromise: true,
    },
    sessionId,
  );
};

export const startCaptchaMonitoring = async (
  cdpSession: CDP,
  config = { diffThreshold: 5, screenshotQuality: 50 },
  executionContextId: number,
  sessionId: string,
) => {
  const script = `
    (async () => {
      if (window.captchaDetector) {
        // Send a message to trigger captcha monitoring
        window.postMessage({
          type: 'START_CAPTCHA_MONITORING',
          config: ${JSON.stringify(config)}
        }, '*');
      } else {
        console.error('Captcha detector not available');
      }
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: script,
      awaitPromise: true,
      contextId: executionContextId,
    },
    sessionId,
  );
};

/**
 * Returns the iframe input detection helper script as a string
 * This script runs in all frames and responds to postMessage requests for input detection
 */
function getIframeInputDetectionScript(): string {
  return `
if (!window.__kakuIframeInputDetectionLoaded__) {
  window.__kakuIframeInputDetectionLoaded__ = true;
  console.log('[IFRAME INPUT] Loading iframe input detection helper script...');

  /**
   * Detects input fields in the current frame and responds via postMessage
   */
  function detectInputsInCurrentFrame(requestData) {
    const inputFields = [];
    const viewportArea = requestData.viewportArea;
    const iframeOffset = requestData.iframeOffset || { x: 0, y: 0 };

    try {
      // Get all input elements in current document
      const inputElements = document.querySelectorAll('input:not([type="button"]):not([type="submit"]):not([type="reset"]):not([type="image"]), textarea, select, [contenteditable="true"]');

      inputElements.forEach((element, index) => {
        try {
          // Skip hidden or disabled elements
          if (element.offsetParent === null || element.disabled ||
              getComputedStyle(element).visibility === 'hidden' ||
              getComputedStyle(element).display === 'none') {
            return;
          }

          const rect = element.getBoundingClientRect();

          // Calculate absolute coordinates relative to main document
          const absoluteRect = {
            x: rect.left + iframeOffset.x + (window.scrollX || 0),
            y: rect.top + iframeOffset.y + (window.scrollY || 0),
            width: rect.width,
            height: rect.height
          };

          // Check if input field is within the viewport area
          if (absoluteRect.x >= viewportArea.x &&
              absoluteRect.y >= viewportArea.y &&
              absoluteRect.x + absoluteRect.width <= viewportArea.x + viewportArea.width &&
              absoluteRect.y + absoluteRect.height <= viewportArea.y + viewportArea.height &&
              absoluteRect.width > 0 && absoluteRect.height > 0) {

            inputFields.push({
              id: element.id || 'iframe-input-' + index,
              tagName: element.tagName,
              type: element.type || 'text',
              x: absoluteRect.x,
              y: absoluteRect.y,
              width: absoluteRect.width,
              height: absoluteRect.height,
              placeholder: element.placeholder || '',
              name: element.name || '',
              context: 'cross-origin-iframe',
              frameInfo: {
                isIframe: window !== window.top,
                url: window.location.href
              }
            });
          }
        } catch (elementErr) {
          console.warn('Error processing input element in iframe:', elementErr);
        }
      });
    } catch (docErr) {
      console.warn('Error accessing document in iframe:', docErr);
    }

    return inputFields;
  }

  // Listen for input detection requests from main document (since there is no access from main document to cross-origin iframes)
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'REQUEST_INPUT_DETECTION') {
      try {
        console.log('[IFRAME INPUT] Received input detection request:', event.data);

        const inputFields = detectInputsInCurrentFrame(event.data);
        const responseData = {
          type: 'IFRAME_INPUT_DETECTION_RESULT',
          requestId: event.data.requestId,
          inputFields: inputFields,
          frameInfo: {
            isIframe: window !== window.top,
            url: window.location.href
          }
        };

        // Send to top window (main document)
        window.top.postMessage(responseData, '*');

        console.log('[IFRAME INPUT] Sent input detection response:', inputFields.length, 'inputs found');
      } catch (err) {
        console.warn('Error handling input detection request in iframe:', err);

        // Send error response
        const errorResponse = {
          type: 'IFRAME_INPUT_DETECTION_RESULT',
          requestId: event.data.requestId,
          inputFields: [],
          error: err.message,
          frameInfo: {
            isIframe: window !== window.top,
            url: window.location.href
          }
        };

        window.top.postMessage(errorResponse, '*');
      }
    }
  });
}
`;
}

/**
 * Sets up iframe input detection helper using CDP Frame Manager approach
 * This ensures the helper script runs in all frames (including cross-origin iframes)
 */
export async function setupInputFocusListener(
  cdpSession: CDP,
  targetSessionId?: string,
): Promise<CDPFrameManager> {
  console.log('🔧 Setting up input focus listener with CDP Frame Manager...');

  // Create and initialize the frame manager
  const frameManager = new CDPFrameManager(cdpSession, targetSessionId);
  await frameManager.initialize();

  // Get the iframe input detection helper script
  const iframeInputDetectionScript = getIframeInputDetectionScript();

  // Use the frame manager to evaluate script on new document
  // This ensures the script runs in all frames including cross-origin iframes
  await frameManager.evaluateOnNewDocument(iframeInputDetectionScript);

  console.log('✓ Iframe input detection helper setup complete with frame manager');

  return frameManager;
}

export const stopCaptchaMonitoring = async (cdpSession: CDP, sessionId?: string) => {
  const script = `
    (async () => {
      if (window.captchaDetector) {
        window.postMessage({ type: 'STOP_CAPTCHA_MONITORING' }, '*');
      }
    })();
  `;

  await cdpSession.Runtime.evaluate(
    {
      expression: script,
      awaitPromise: true,
    },
    sessionId,
  );
};
